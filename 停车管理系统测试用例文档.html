<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>停车管理系统测试用例文档</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .stat-label {
            color: #666;
            font-size: 0.9em;
        }
        
        .search-filter {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .search-input {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 16px;
        }
        
        .module {
            background: white;
            margin-bottom: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .module-header {
            background: #f8f9fa;
            padding: 20px;
            border-bottom: 1px solid #eee;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .module-header:hover {
            background: #e9ecef;
        }
        
        .module-title {
            font-size: 1.3em;
            font-weight: bold;
            color: #495057;
        }
        
        .module-progress {
            font-size: 0.9em;
            color: #6c757d;
        }
        
        .module-content {
            display: none;
            padding: 0;
        }
        
        .module-content.active {
            display: block;
        }
        
        .test-case {
            border-bottom: 1px solid #eee;
            padding: 20px;
        }
        
        .test-case:last-child {
            border-bottom: none;
        }
        
        .test-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .test-title {
            font-weight: bold;
            font-size: 1.1em;
            color: #495057;
        }
        
        .test-status {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.8em;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .status-pending {
            background: #ffeaa7;
            color: #d63031;
        }
        
        .status-running {
            background: #74b9ff;
            color: white;
        }
        
        .status-passed {
            background: #00b894;
            color: white;
        }
        
        .status-failed {
            background: #e17055;
            color: white;
        }
        
        .test-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 15px;
        }
        
        .test-steps, .test-expected {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
        }
        
        .test-steps h4, .test-expected h4 {
            margin-bottom: 10px;
            color: #495057;
        }
        
        .test-steps ol {
            margin-left: 20px;
        }
        
        .test-steps li {
            margin-bottom: 5px;
        }
        
        .toggle-icon {
            transition: transform 0.3s;
        }
        
        .toggle-icon.rotated {
            transform: rotate(180deg);
        }
        
        @media (max-width: 768px) {
            .test-details {
                grid-template-columns: 1fr;
            }
            
            .stats {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>停车管理系统测试用例文档</h1>
            <p>管理员端功能测试 - 覆盖所有接口和功能点</p>
        </div>
        
        <div class="stats">
            <div class="stat-card">
                <div class="stat-number" id="totalTests">0</div>
                <div class="stat-label">总测试用例</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="passedTests">0</div>
                <div class="stat-label">已通过</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="failedTests">0</div>
                <div class="stat-label">失败</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="progressPercent">0%</div>
                <div class="stat-label">完成进度</div>
            </div>
        </div>
        
        <div class="search-filter">
            <input type="text" class="search-input" placeholder="搜索测试用例..." id="searchInput">
        </div>
        
        <!-- 登录认证模块 -->
        <div class="module">
            <div class="module-header" onclick="toggleModule(this)">
                <div>
                    <div class="module-title">🔐 登录认证模块</div>
                    <div class="module-progress">0/8 完成</div>
                </div>
                <span class="toggle-icon">▼</span>
            </div>
            <div class="module-content">
                <div class="test-case">
                    <div class="test-header">
                        <div class="test-title">TC001 - 正常登录流程</div>
                        <div class="test-status status-pending" onclick="changeStatus(this)">待测试</div>
                    </div>
                    <div class="test-details">
                        <div class="test-steps">
                            <h4>测试步骤：</h4>
                            <ol>
                                <li>打开登录页面</li>
                                <li>输入正确的用户名和密码</li>
                                <li>输入正确的验证码</li>
                                <li>点击登录按钮</li>
                            </ol>
                        </div>
                        <div class="test-expected">
                            <h4>预期结果：</h4>
                            <p>成功登录，跳转到首页，显示用户信息</p>
                        </div>
                    </div>
                </div>
                
                <div class="test-case">
                    <div class="test-header">
                        <div class="test-title">TC002 - 用户名密码错误</div>
                        <div class="test-status status-pending" onclick="changeStatus(this)">待测试</div>
                    </div>
                    <div class="test-details">
                        <div class="test-steps">
                            <h4>测试步骤：</h4>
                            <ol>
                                <li>打开登录页面</li>
                                <li>输入错误的用户名或密码</li>
                                <li>输入正确的验证码</li>
                                <li>点击登录按钮</li>
                            </ol>
                        </div>
                        <div class="test-expected">
                            <h4>预期结果：</h4>
                            <p>显示"用户名或密码错误"提示信息</p>
                        </div>
                    </div>
                </div>
                
                <div class="test-case">
                    <div class="test-header">
                        <div class="test-title">TC003 - 验证码错误</div>
                        <div class="test-status status-pending" onclick="changeStatus(this)">待测试</div>
                    </div>
                    <div class="test-details">
                        <div class="test-steps">
                            <h4>测试步骤：</h4>
                            <ol>
                                <li>打开登录页面</li>
                                <li>输入正确的用户名和密码</li>
                                <li>输入错误的验证码</li>
                                <li>点击登录按钮</li>
                            </ol>
                        </div>
                        <div class="test-expected">
                            <h4>预期结果：</h4>
                            <p>显示"验证码错误"提示信息</p>
                        </div>
                    </div>
                </div>
                
                <div class="test-case">
                    <div class="test-header">
                        <div class="test-title">TC004 - 验证码刷新功能</div>
                        <div class="test-status status-pending" onclick="changeStatus(this)">待测试</div>
                    </div>
                    <div class="test-details">
                        <div class="test-steps">
                            <h4>测试步骤：</h4>
                            <ol>
                                <li>打开登录页面</li>
                                <li>点击验证码图片</li>
                                <li>观察验证码是否刷新</li>
                            </ol>
                        </div>
                        <div class="test-expected">
                            <h4>预期结果：</h4>
                            <p>验证码图片刷新，显示新的验证码</p>
                        </div>
                    </div>
                </div>
                
                <div class="test-case">
                    <div class="test-header">
                        <div class="test-title">TC005 - 空字段验证</div>
                        <div class="test-status status-pending" onclick="changeStatus(this)">待测试</div>
                    </div>
                    <div class="test-details">
                        <div class="test-steps">
                            <h4>测试步骤：</h4>
                            <ol>
                                <li>打开登录页面</li>
                                <li>不输入用户名、密码或验证码</li>
                                <li>点击登录按钮</li>
                            </ol>
                        </div>
                        <div class="test-expected">
                            <h4>预期结果：</h4>
                            <p>显示相应的必填字段提示信息</p>
                        </div>
                    </div>
                </div>
                
                <div class="test-case">
                    <div class="test-header">
                        <div class="test-title">TC006 - 登出功能</div>
                        <div class="test-status status-pending" onclick="changeStatus(this)">待测试</div>
                    </div>
                    <div class="test-details">
                        <div class="test-steps">
                            <h4>测试步骤：</h4>
                            <ol>
                                <li>成功登录系统</li>
                                <li>点击用户头像或菜单</li>
                                <li>点击退出登录</li>
                            </ol>
                        </div>
                        <div class="test-expected">
                            <h4>预期结果：</h4>
                            <p>成功退出，跳转到登录页面，清除登录状态</p>
                        </div>
                    </div>
                </div>
                
                <div class="test-case">
                    <div class="test-header">
                        <div class="test-title">TC007 - Token过期处理</div>
                        <div class="test-status status-pending" onclick="changeStatus(this)">待测试</div>
                    </div>
                    <div class="test-details">
                        <div class="test-steps">
                            <h4>测试步骤：</h4>
                            <ol>
                                <li>登录系统</li>
                                <li>等待Token过期或手动清除Token</li>
                                <li>尝试访问需要权限的页面</li>
                            </ol>
                        </div>
                        <div class="test-expected">
                            <h4>预期结果：</h4>
                            <p>自动跳转到登录页面，提示重新登录</p>
                        </div>
                    </div>
                </div>
                
                <div class="test-case">
                    <div class="test-header">
                        <div class="test-title">TC008 - 记住密码功能</div>
                        <div class="test-status status-pending" onclick="changeStatus(this)">待测试</div>
                    </div>
                    <div class="test-details">
                        <div class="test-steps">
                            <h4>测试步骤：</h4>
                            <ol>
                                <li>登录时勾选"记住密码"</li>
                                <li>成功登录后退出</li>
                                <li>重新打开登录页面</li>
                            </ol>
                        </div>
                        <div class="test-expected">
                            <h4>预期结果：</h4>
                            <p>用户名和密码自动填充</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 平台管理模块 -->
        <div class="module">
            <div class="module-header" onclick="toggleModule(this)">
                <div>
                    <div class="module-title">🏢 平台管理模块</div>
                    <div class="module-progress">0/12 完成</div>
                </div>
                <span class="toggle-icon">▼</span>
            </div>
            <div class="module-content">
                <div class="test-case">
                    <div class="test-header">
                        <div class="test-title">TC024 - 运营商管理-新增运营商</div>
                        <div class="test-status status-pending" onclick="changeStatus(this)">待测试</div>
                    </div>
                    <div class="test-details">
                        <div class="test-steps">
                            <h4>测试步骤：</h4>
                            <ol>
                                <li>进入平台管理 > 运营商管理页面</li>
                                <li>点击"新增"按钮</li>
                                <li>填写公司名称、联系人、电话、地址等信息</li>
                                <li>设置状态为正常</li>
                                <li>点击确定保存</li>
                            </ol>
                        </div>
                        <div class="test-expected">
                            <h4>预期结果：</h4>
                            <p>运营商创建成功，列表中显示新运营商信息</p>
                        </div>
                    </div>
                </div>

                <div class="test-case">
                    <div class="test-header">
                        <div class="test-title">TC025 - 运营商管理-公司名称唯一性验证</div>
                        <div class="test-status status-pending" onclick="changeStatus(this)">待测试</div>
                    </div>
                    <div class="test-details">
                        <div class="test-steps">
                            <h4>测试步骤：</h4>
                            <ol>
                                <li>尝试新增运营商</li>
                                <li>输入已存在的公司名称</li>
                                <li>填写其他必填信息</li>
                                <li>点击确定保存</li>
                            </ol>
                        </div>
                        <div class="test-expected">
                            <h4>预期结果：</h4>
                            <p>显示"公司名称已存在"的错误提示</p>
                        </div>
                    </div>
                </div>

                <div class="test-case">
                    <div class="test-header">
                        <div class="test-title">TC026 - 运营商管理-删除前关联检查</div>
                        <div class="test-status status-pending" onclick="changeStatus(this)">待测试</div>
                    </div>
                    <div class="test-details">
                        <div class="test-steps">
                            <h4>测试步骤：</h4>
                            <ol>
                                <li>选择一个有关联场库的运营商</li>
                                <li>点击删除按钮</li>
                                <li>确认删除操作</li>
                            </ol>
                        </div>
                        <div class="test-expected">
                            <h4>预期结果：</h4>
                            <p>显示"该运营商下还有场库，无法删除"的提示</p>
                        </div>
                    </div>
                </div>

                <div class="test-case">
                    <div class="test-header">
                        <div class="test-title">TC027 - 场库管理-新增场库</div>
                        <div class="test-status status-pending" onclick="changeStatus(this)">待测试</div>
                    </div>
                    <div class="test-details">
                        <div class="test-steps">
                            <h4>测试步骤：</h4>
                            <ol>
                                <li>进入平台管理 > 场库管理页面</li>
                                <li>点击"新增"按钮</li>
                                <li>填写场库名称、编号、地址等基本信息</li>
                                <li>选择所属运营商</li>
                                <li>设置车位数量、收费标准等</li>
                                <li>点击确定保存</li>
                            </ol>
                        </div>
                        <div class="test-expected">
                            <h4>预期结果：</h4>
                            <p>场库创建成功，列表中显示新场库信息</p>
                        </div>
                    </div>
                </div>

                <div class="test-case">
                    <div class="test-header">
                        <div class="test-title">TC028 - 场库管理-场库编号唯一性验证</div>
                        <div class="test-status status-pending" onclick="changeStatus(this)">待测试</div>
                    </div>
                    <div class="test-details">
                        <div class="test-steps">
                            <h4>测试步骤：</h4>
                            <ol>
                                <li>尝试新增场库</li>
                                <li>输入已存在的场库编号</li>
                                <li>填写其他必填信息</li>
                                <li>点击确定保存</li>
                            </ol>
                        </div>
                        <div class="test-expected">
                            <h4>预期结果：</h4>
                            <p>显示"场库编号已存在"的错误提示</p>
                        </div>
                    </div>
                </div>

                <div class="test-case">
                    <div class="test-header">
                        <div class="test-title">TC029 - 场库管理-车位数量验证</div>
                        <div class="test-status status-pending" onclick="changeStatus(this)">待测试</div>
                    </div>
                    <div class="test-details">
                        <div class="test-steps">
                            <h4>测试步骤：</h4>
                            <ol>
                                <li>编辑场库信息</li>
                                <li>设置剩余车位数大于总车位数</li>
                                <li>点击确定保存</li>
                            </ol>
                        </div>
                        <div class="test-expected">
                            <h4>预期结果：</h4>
                            <p>显示"剩余车位不能大于总车位"的错误提示</p>
                        </div>
                    </div>
                </div>

                <div class="test-case">
                    <div class="test-header">
                        <div class="test-title">TC030 - 场库管理-子场库管理</div>
                        <div class="test-status status-pending" onclick="changeStatus(this)">待测试</div>
                    </div>
                    <div class="test-details">
                        <div class="test-steps">
                            <h4>测试步骤：</h4>
                            <ol>
                                <li>在场库列表中点击"子场库"按钮</li>
                                <li>在弹窗中点击"新增子场库"</li>
                                <li>填写子场库信息（名称、楼层、车位等）</li>
                                <li>保存子场库</li>
                                <li>验证子场库列表显示</li>
                            </ol>
                        </div>
                        <div class="test-expected">
                            <h4>预期结果：</h4>
                            <p>子场库创建成功，在子场库列表中显示</p>
                        </div>
                    </div>
                </div>

                <div class="test-case">
                    <div class="test-header">
                        <div class="test-title">TC031 - 场库管理-按运营商筛选</div>
                        <div class="test-status status-pending" onclick="changeStatus(this)">待测试</div>
                    </div>
                    <div class="test-details">
                        <div class="test-steps">
                            <h4>测试步骤：</h4>
                            <ol>
                                <li>在场库管理页面的搜索条件中</li>
                                <li>选择特定的运营商</li>
                                <li>点击搜索按钮</li>
                                <li>验证搜索结果</li>
                            </ol>
                        </div>
                        <div class="test-expected">
                            <h4>预期结果：</h4>
                            <p>只显示该运营商下的场库</p>
                        </div>
                    </div>
                </div>

                <div class="test-case">
                    <div class="test-header">
                        <div class="test-title">TC032 - 场库管理人员-新增管理人员</div>
                        <div class="test-status status-pending" onclick="changeStatus(this)">待测试</div>
                    </div>
                    <div class="test-details">
                        <div class="test-steps">
                            <h4>测试步骤：</h4>
                            <ol>
                                <li>进入平台管理 > 场库管理人员页面</li>
                                <li>点击"新增"按钮</li>
                                <li>选择运营商和场库</li>
                                <li>填写管理人员信息（姓名、电话、职位等）</li>
                                <li>设置是否为主要负责人</li>
                                <li>点击确定保存</li>
                            </ol>
                        </div>
                        <div class="test-expected">
                            <h4>预期结果：</h4>
                            <p>管理人员创建成功，列表中显示新管理人员</p>
                        </div>
                    </div>
                </div>

                <div class="test-case">
                    <div class="test-header">
                        <div class="test-title">TC033 - 场库管理人员-主要负责人唯一性</div>
                        <div class="test-status status-pending" onclick="changeStatus(this)">待测试</div>
                    </div>
                    <div class="test-details">
                        <div class="test-steps">
                            <h4>测试步骤：</h4>
                            <ol>
                                <li>为同一个场库添加第二个主要负责人</li>
                                <li>勾选"主要负责人"选项</li>
                                <li>点击确定保存</li>
                            </ol>
                        </div>
                        <div class="test-expected">
                            <h4>预期结果：</h4>
                            <p>显示"该场库已有主要负责人"的错误提示</p>
                        </div>
                    </div>
                </div>

                <div class="test-case">
                    <div class="test-header">
                        <div class="test-title">TC034 - 场库管理人员-级联选择</div>
                        <div class="test-status status-pending" onclick="changeStatus(this)">待测试</div>
                    </div>
                    <div class="test-details">
                        <div class="test-steps">
                            <h4>测试步骤：</h4>
                            <ol>
                                <li>在新增管理人员页面</li>
                                <li>先选择运营商</li>
                                <li>观察场库下拉列表的变化</li>
                                <li>选择场库</li>
                            </ol>
                        </div>
                        <div class="test-expected">
                            <h4>预期结果：</h4>
                            <p>场库列表只显示该运营商下的场库</p>
                        </div>
                    </div>
                </div>

                <div class="test-case">
                    <div class="test-header">
                        <div class="test-title">TC035 - 平台管理-联系方式验证</div>
                        <div class="test-status status-pending" onclick="changeStatus(this)">待测试</div>
                    </div>
                    <div class="test-details">
                        <div class="test-steps">
                            <h4>测试步骤：</h4>
                            <ol>
                                <li>在各个管理页面输入错误格式的手机号</li>
                                <li>输入错误格式的邮箱地址</li>
                                <li>尝试保存</li>
                            </ol>
                        </div>
                        <div class="test-expected">
                            <h4>预期结果：</h4>
                            <p>显示相应的格式错误提示</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 订单管理模块 -->
        <div class="module">
            <div class="module-header" onclick="toggleModule(this)">
                <div>
                    <div class="module-title">🧾 订单管理模块</div>
                    <div class="module-progress">0/10 完成</div>
                </div>
                <span class="toggle-icon">▼</span>
            </div>
            <div class="module-content">
                <div class="test-case">
                    <div class="test-header">
                        <div class="test-title">TC036 - 停车订单-订单查询</div>
                        <div class="test-status status-pending" onclick="changeStatus(this)">待测试</div>
                    </div>
                    <div class="test-details">
                        <div class="test-steps">
                            <h4>测试步骤：</h4>
                            <ol>
                                <li>进入订单管理 > 停车订单页面</li>
                                <li>输入车牌号进行搜索</li>
                                <li>选择场库进行筛选</li>
                                <li>选择支付状态进行筛选</li>
                                <li>设置时间范围进行筛选</li>
                                <li>点击搜索按钮</li>
                            </ol>
                        </div>
                        <div class="test-expected">
                            <h4>预期结果：</h4>
                            <p>显示符合条件的订单记录</p>
                        </div>
                    </div>
                </div>

                <div class="test-case">
                    <div class="test-header">
                        <div class="test-title">TC037 - 停车订单-订单详情</div>
                        <div class="test-status status-pending" onclick="changeStatus(this)">待测试</div>
                    </div>
                    <div class="test-details">
                        <div class="test-steps">
                            <h4>测试步骤：</h4>
                            <ol>
                                <li>在订单列表中点击"查看"按钮</li>
                                <li>查看订单详情弹窗</li>
                                <li>验证订单信息的完整性</li>
                            </ol>
                        </div>
                        <div class="test-expected">
                            <h4>预期结果：</h4>
                            <p>显示完整的订单详情，包括车牌号、场库、金额、支付状态等</p>
                        </div>
                    </div>
                </div>

                <div class="test-case">
                    <div class="test-header">
                        <div class="test-title">TC038 - 停车订单-订单退款</div>
                        <div class="test-status status-pending" onclick="changeStatus(this)">待测试</div>
                    </div>
                    <div class="test-details">
                        <div class="test-steps">
                            <h4>测试步骤：</h4>
                            <ol>
                                <li>选择一个已支付的订单</li>
                                <li>点击"退款"按钮</li>
                                <li>输入退款金额和原因</li>
                                <li>确认退款操作</li>
                                <li>验证订单状态变化</li>
                            </ol>
                        </div>
                        <div class="test-expected">
                            <h4>预期结果：</h4>
                            <p>退款成功，订单状态变为"已退款"</p>
                        </div>
                    </div>
                </div>

                <div class="test-case">
                    <div class="test-header">
                        <div class="test-title">TC039 - 停车订单-导出订单</div>
                        <div class="test-status status-pending" onclick="changeStatus(this)">待测试</div>
                    </div>
                    <div class="test-details">
                        <div class="test-steps">
                            <h4>测试步骤：</h4>
                            <ol>
                                <li>在订单列表页面</li>
                                <li>设置筛选条件（可选）</li>
                                <li>点击"导出"按钮</li>
                                <li>检查导出的文件</li>
                            </ol>
                        </div>
                        <div class="test-expected">
                            <h4>预期结果：</h4>
                            <p>成功导出订单数据，文件内容与筛选条件一致</p>
                        </div>
                    </div>
                </div>

                <div class="test-case">
                    <div class="test-header">
                        <div class="test-title">TC040 - 车辆出入场-记录查询</div>
                        <div class="test-status status-pending" onclick="changeStatus(this)">待测试</div>
                    </div>
                    <div class="test-details">
                        <div class="test-steps">
                            <h4>测试步骤：</h4>
                            <ol>
                                <li>进入订单管理 > 车辆出入场记录页面</li>
                                <li>输入车牌号进行搜索</li>
                                <li>选择场库进行筛选</li>
                                <li>选择车辆状态（在场/已出场）</li>
                                <li>设置入场/出场时间范围</li>
                                <li>点击搜索按钮</li>
                            </ol>
                        </div>
                        <div class="test-expected">
                            <h4>预期结果：</h4>
                            <p>显示符合条件的车辆出入场记录</p>
                        </div>
                    </div>
                </div>

                <div class="test-case">
                    <div class="test-header">
                        <div class="test-title">TC041 - 车辆出入场-记录详情</div>
                        <div class="test-status status-pending" onclick="changeStatus(this)">待测试</div>
                    </div>
                    <div class="test-details">
                        <div class="test-steps">
                            <h4>测试步骤：</h4>
                            <ol>
                                <li>在出入场记录列表中点击"查看"按钮</li>
                                <li>查看记录详情弹窗</li>
                                <li>验证记录信息的完整性</li>
                                <li>查看入场/出场照片</li>
                            </ol>
                        </div>
                        <div class="test-expected">
                            <h4>预期结果：</h4>
                            <p>显示完整的出入场记录，包括车牌号、场库、时间、照片等</p>
                        </div>
                    </div>
                </div>

                <div class="test-case">
                    <div class="test-header">
                        <div class="test-title">TC042 - 车辆出入场-手动添加记录</div>
                        <div class="test-status status-pending" onclick="changeStatus(this)">待测试</div>
                    </div>
                    <div class="test-details">
                        <div class="test-steps">
                            <h4>测试步骤：</h4>
                            <ol>
                                <li>点击"新增"按钮</li>
                                <li>填写车牌号、场库、车辆类型等信息</li>
                                <li>设置入场时间</li>
                                <li>上传入场照片（可选）</li>
                                <li>点击确定保存</li>
                            </ol>
                        </div>
                        <div class="test-expected">
                            <h4>预期结果：</h4>
                            <p>记录添加成功，在列表中显示</p>
                        </div>
                    </div>
                </div>

                <div class="test-case">
                    <div class="test-header">
                        <div class="test-title">TC043 - 车辆出入场-记录编辑</div>
                        <div class="test-status status-pending" onclick="changeStatus(this)">待测试</div>
                    </div>
                    <div class="test-details">
                        <div class="test-steps">
                            <h4>测试步骤：</h4>
                            <ol>
                                <li>在记录列表中点击"修改"按钮</li>
                                <li>修改车牌号或其他信息</li>
                                <li>点击确定保存</li>
                                <li>验证修改是否生效</li>
                            </ol>
                        </div>
                        <div class="test-expected">
                            <h4>预期结果：</h4>
                            <p>记录修改成功，显示更新后的信息</p>
                        </div>
                    </div>
                </div>

                <div class="test-case">
                    <div class="test-header">
                        <div class="test-title">TC044 - 车辆出入场-记录删除</div>
                        <div class="test-status status-pending" onclick="changeStatus(this)">待测试</div>
                    </div>
                    <div class="test-details">
                        <div class="test-steps">
                            <h4>测试步骤：</h4>
                            <ol>
                                <li>在记录列表中选择一条记录</li>
                                <li>点击"删除"按钮</li>
                                <li>确认删除操作</li>
                                <li>验证记录是否被删除</li>
                            </ol>
                        </div>
                        <div class="test-expected">
                            <h4>预期结果：</h4>
                            <p>记录删除成功，不再显示在列表中</p>
                        </div>
                    </div>
                </div>

                <div class="test-case">
                    <div class="test-header">
                        <div class="test-title">TC045 - 订单管理-数据统计</div>
                        <div class="test-status status-pending" onclick="changeStatus(this)">待测试</div>
                    </div>
                    <div class="test-details">
                        <div class="test-steps">
                            <h4>测试步骤：</h4>
                            <ol>
                                <li>查看订单列表页面的统计信息</li>
                                <li>验证订单总数、金额等统计数据</li>
                                <li>切换不同的筛选条件</li>
                                <li>验证统计数据的变化</li>
                            </ol>
                        </div>
                        <div class="test-expected">
                            <h4>预期结果：</h4>
                            <p>统计数据准确，与筛选条件一致</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 道闸控制模块 -->
        <div class="module">
            <div class="module-header" onclick="toggleModule(this)">
                <div>
                    <div class="module-title">🚧 道闸控制模块</div>
                    <div class="module-progress">0/8 完成</div>
                </div>
                <span class="toggle-icon">▼</span>
            </div>
            <div class="module-content">
                <div class="test-case">
                    <div class="test-header">
                        <div class="test-title">TC046 - 道闸控制-查询停车费</div>
                        <div class="test-status status-pending" onclick="changeStatus(this)">待测试</div>
                    </div>
                    <div class="test-details">
                        <div class="test-steps">
                            <h4>测试步骤：</h4>
                            <ol>
                                <li>通过API调用查询停车费接口</li>
                                <li>传入场库ID和车牌号</li>
                                <li>验证返回的费用信息</li>
                                <li>测试不同车辆类型的费用计算</li>
                            </ol>
                        </div>
                        <div class="test-expected">
                            <h4>预期结果：</h4>
                            <p>正确返回停车费用信息，计算准确</p>
                        </div>
                    </div>
                </div>

                <div class="test-case">
                    <div class="test-header">
                        <div class="test-title">TC047 - 道闸控制-无牌车入场</div>
                        <div class="test-status status-pending" onclick="changeStatus(this)">待测试</div>
                    </div>
                    <div class="test-details">
                        <div class="test-steps">
                            <h4>测试步骤：</h4>
                            <ol>
                                <li>调用无牌车入场接口</li>
                                <li>传入场库ID和道闸号</li>
                                <li>验证入场记录是否创建</li>
                                <li>检查道闸开启状态</li>
                            </ol>
                        </div>
                        <div class="test-expected">
                            <h4>预期结果：</h4>
                            <p>无牌车入场成功，创建入场记录</p>
                        </div>
                    </div>
                </div>

                <div class="test-case">
                    <div class="test-header">
                        <div class="test-title">TC048 - 道闸控制-支付订单通知</div>
                        <div class="test-status status-pending" onclick="changeStatus(this)">待测试</div>
                    </div>
                    <div class="test-details">
                        <div class="test-steps">
                            <h4>测试步骤：</h4>
                            <ol>
                                <li>调用支付订单通知接口</li>
                                <li>传入订单号和支付信息</li>
                                <li>验证道闸系统接收通知</li>
                                <li>检查出场权限是否开启</li>
                            </ol>
                        </div>
                        <div class="test-expected">
                            <h4>预期结果：</h4>
                            <p>支付通知成功，车辆可以正常出场</p>
                        </div>
                    </div>
                </div>

                <div class="test-case">
                    <div class="test-header">
                        <div class="test-title">TC049 - 道闸控制-查询车辆套餐</div>
                        <div class="test-status status-pending" onclick="changeStatus(this)">待测试</div>
                    </div>
                    <div class="test-details">
                        <div class="test-steps">
                            <h4>测试步骤：</h4>
                            <ol>
                                <li>调用查询车辆套餐接口</li>
                                <li>传入场库ID和车牌号</li>
                                <li>验证返回的套餐信息</li>
                                <li>测试不同套餐类型的查询</li>
                            </ol>
                        </div>
                        <div class="test-expected">
                            <h4>预期结果：</h4>
                            <p>正确返回车辆套餐信息</p>
                        </div>
                    </div>
                </div>

                <div class="test-case">
                    <div class="test-header">
                        <div class="test-title">TC050 - 道闸控制-接口签名验证</div>
                        <div class="test-status status-pending" onclick="changeStatus(this)">待测试</div>
                    </div>
                    <div class="test-details">
                        <div class="test-steps">
                            <h4>测试步骤：</h4>
                            <ol>
                                <li>使用错误的签名调用接口</li>
                                <li>使用过期的时间戳</li>
                                <li>验证接口的安全性</li>
                                <li>测试正确签名的接口调用</li>
                            </ol>
                        </div>
                        <div class="test-expected">
                            <h4>预期结果：</h4>
                            <p>错误签名被拒绝，正确签名通过验证</p>
                        </div>
                    </div>
                </div>

                <div class="test-case">
                    <div class="test-header">
                        <div class="test-title">TC051 - 道闸控制-异常处理</div>
                        <div class="test-status status-pending" onclick="changeStatus(this)">待测试</div>
                    </div>
                    <div class="test-details">
                        <div class="test-steps">
                            <h4>测试步骤：</h4>
                            <ol>
                                <li>传入不存在的场库ID</li>
                                <li>传入无效的车牌号格式</li>
                                <li>测试网络超时情况</li>
                                <li>验证错误信息返回</li>
                            </ol>
                        </div>
                        <div class="test-expected">
                            <h4>预期结果：</h4>
                            <p>返回明确的错误信息，系统稳定运行</p>
                        </div>
                    </div>
                </div>

                <div class="test-case">
                    <div class="test-header">
                        <div class="test-title">TC052 - 道闸控制-日志记录</div>
                        <div class="test-status status-pending" onclick="changeStatus(this)">待测试</div>
                    </div>
                    <div class="test-details">
                        <div class="test-steps">
                            <h4>测试步骤：</h4>
                            <ol>
                                <li>执行各种道闸控制操作</li>
                                <li>检查系统日志记录</li>
                                <li>验证日志内容的完整性</li>
                                <li>确认敏感信息是否被正确处理</li>
                            </ol>
                        </div>
                        <div class="test-expected">
                            <h4>预期结果：</h4>
                            <p>所有操作都有详细的日志记录</p>
                        </div>
                    </div>
                </div>

                <div class="test-case">
                    <div class="test-header">
                        <div class="test-title">TC053 - 道闸控制-性能测试</div>
                        <div class="test-status status-pending" onclick="changeStatus(this)">待测试</div>
                    </div>
                    <div class="test-details">
                        <div class="test-steps">
                            <h4>测试步骤：</h4>
                            <ol>
                                <li>并发调用道闸控制接口</li>
                                <li>测试高频率的查询请求</li>
                                <li>监控响应时间</li>
                                <li>验证系统稳定性</li>
                            </ol>
                        </div>
                        <div class="test-expected">
                            <h4>预期结果：</h4>
                            <p>系统能够处理高并发请求，响应时间在可接受范围内</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 统计数据
        let stats = {
            total: 0,
            passed: 0,
            failed: 0,
            running: 0
        };

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateStats();
            initSearch();
        });

        // 切换模块显示/隐藏
        function toggleModule(header) {
            const content = header.nextElementSibling;
            const icon = header.querySelector('.toggle-icon');

            if (content.classList.contains('active')) {
                content.classList.remove('active');
                icon.classList.remove('rotated');
            } else {
                content.classList.add('active');
                icon.classList.add('rotated');
            }
        }

        // 改变测试状态
        function changeStatus(statusElement) {
            const statuses = ['status-pending', 'status-running', 'status-passed', 'status-failed'];
            const statusTexts = ['待测试', '进行中', '已通过', '失败'];

            let currentIndex = 0;
            for (let i = 0; i < statuses.length; i++) {
                if (statusElement.classList.contains(statuses[i])) {
                    currentIndex = i;
                    break;
                }
            }

            // 移除当前状态
            statusElement.classList.remove(statuses[currentIndex]);

            // 切换到下一个状态
            const nextIndex = (currentIndex + 1) % statuses.length;
            statusElement.classList.add(statuses[nextIndex]);
            statusElement.textContent = statusTexts[nextIndex];

            updateStats();
            updateModuleProgress();
        }

        // 更新统计数据
        function updateStats() {
            const allStatuses = document.querySelectorAll('.test-status');
            stats.total = allStatuses.length;
            stats.passed = document.querySelectorAll('.status-passed').length;
            stats.failed = document.querySelectorAll('.status-failed').length;
            stats.running = document.querySelectorAll('.status-running').length;

            document.getElementById('totalTests').textContent = stats.total;
            document.getElementById('passedTests').textContent = stats.passed;
            document.getElementById('failedTests').textContent = stats.failed;

            const progress = stats.total > 0 ? Math.round(((stats.passed + stats.failed) / stats.total) * 100) : 0;
            document.getElementById('progressPercent').textContent = progress + '%';
        }

        // 更新模块进度
        function updateModuleProgress() {
            const modules = document.querySelectorAll('.module');
            modules.forEach(module => {
                const moduleTests = module.querySelectorAll('.test-status');
                const moduleCompleted = module.querySelectorAll('.status-passed, .status-failed');
                const progressElement = module.querySelector('.module-progress');

                if (progressElement) {
                    progressElement.textContent = `${moduleCompleted.length}/${moduleTests.length} 完成`;
                }
            });
        }

        // 初始化搜索功能
        function initSearch() {
            const searchInput = document.getElementById('searchInput');
            searchInput.addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase();
                const testCases = document.querySelectorAll('.test-case');

                testCases.forEach(testCase => {
                    const title = testCase.querySelector('.test-title').textContent.toLowerCase();
                    const steps = testCase.querySelector('.test-steps').textContent.toLowerCase();
                    const expected = testCase.querySelector('.test-expected').textContent.toLowerCase();

                    if (title.includes(searchTerm) || steps.includes(searchTerm) || expected.includes(searchTerm)) {
                        testCase.style.display = 'block';
                    } else {
                        testCase.style.display = 'none';
                    }
                });
            });
        }

        // 导出测试报告
        function exportReport() {
            const report = {
                timestamp: new Date().toISOString(),
                stats: stats,
                modules: []
            };

            const modules = document.querySelectorAll('.module');
            modules.forEach(module => {
                const moduleTitle = module.querySelector('.module-title').textContent;
                const testCases = module.querySelectorAll('.test-case');
                const moduleData = {
                    title: moduleTitle,
                    tests: []
                };

                testCases.forEach(testCase => {
                    const title = testCase.querySelector('.test-title').textContent;
                    const status = testCase.querySelector('.test-status').textContent;
                    moduleData.tests.push({ title, status });
                });

                report.modules.push(moduleData);
            });

            const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `测试报告_${new Date().toISOString().split('T')[0]}.json`;
            a.click();
            URL.revokeObjectURL(url);
        }
    </script>
</body>
</html>
