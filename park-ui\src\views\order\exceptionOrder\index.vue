<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="车牌号" prop="plateNum">
        <el-input v-model="queryParams.plateNum" placeholder="请输入车牌号" clearable style="width: 200px"
          @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="场库" prop="parkingId">
        <el-cascader v-model="queryParams.parkingId" :options="warehouseCascaderOptions" :props="{
            value: 'value',
            label: 'label',
            children: 'children',
            emitPath: false,
            checkStrictly: true,
            expandTrigger: 'hover'
          }" placeholder="请选择场库" style="width: 200px" clearable filterable :show-all-levels="false" />
      </el-form-item>
      <el-form-item label="错误类型" prop="errCode">
        <el-select v-model="queryParams.errCode" placeholder="请选择错误类型" clearable style="width: 200px">
          <el-option v-for="dict in error_data_code" :key="dict.value" :label="dict.label"
            :value="parseInt(dict.value)" />
        </el-select>
      </el-form-item>
      <el-form-item label="更新时间" prop="updateDate">
        <el-date-picker v-model="updateDate" style="width: 200px" value-format="YYYY-MM-DD" type="date"
          placeholder="选择更新日期"></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <!-- <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['system:errorDataLog:add']"
        >新增</el-button>
      </el-col> -->
      <el-col :span="1.5">
        <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['system:errorDataLog:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Download" @click="handleExport"
          v-hasPermi="['system:errorDataLog:export']">导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="errorDataLogList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="场库名称" align="center" prop="parkingName" min-width="120">
        <template #default="scope">
          <span>{{ scope.row.parkingName || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="车牌号" align="center" prop="plateNum" width="120">
        <template #default="scope">
          <el-tag v-if="scope.row.plateNum" :type="getPlateNoTagType(scope.row.plateNum)" :color="getPlateNoColor(scope.row.plateNum)"
            effect="plain">
            {{ scope.row.plateNum }}
          </el-tag>
          <span v-else>--</span>
        </template>
      </el-table-column>
      <el-table-column label="错误类型" align="center" prop="errCode" width="120">
        <template #default="scope">
          <div class="error-type-wrapper">
            <dict-tag :options="error_data_code" :value="scope.row.errCode" />
          </div>
        </template>
      </el-table-column>
      <el-table-column label="入场时间" align="center" prop="inTime" min-width="150">
        <template #default="scope">
          <span v-if="scope.row.inTime">{{ scope.row.inTime }}</span>
          <span v-else>--</span>
        </template>
      </el-table-column>
      <el-table-column label="出场时间" align="center" prop="outTime" min-width="150">
        <template #default="scope">
          <span v-if="scope.row.outTime">{{ scope.row.outTime }}</span>
          <span v-else>--</span>
        </template>
      </el-table-column>
      <el-table-column label="金额" align="center" prop="money" width="100">
        <template #default="scope">
          <span v-if="scope.row.money !== null && scope.row.money !== undefined" class="money-text">¥{{ scope.row.money }}</span>
          <span v-else>--</span>
        </template>
      </el-table-column>
      <el-table-column label="备注" align="center" prop="remark" min-width="200" show-overflow-tooltip>
        <template #default="scope">
          <span>{{ scope.row.remark || '--' }}</span>
        </template>
      </el-table-column>

      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" fixed="right" width="160">
        <template #default="scope">
          <el-button link type="primary" icon="View" @click="handleView(scope.row)"
            v-hasPermi="['system:errorDataLog:query']">查看</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
            v-hasPermi="['system:errorDataLog:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <custom-pagination :total="total" v-model:current-page="queryParams.pageNum"
      v-model:page-size="queryParams.pageSize" @pagination="getList" />

    <!-- 错误数据日志对话框 -->
    <el-dialog :title="title" v-model="open" width="1000px" append-to-body>
      <!-- 查看模式：美化展示 -->
      <div class="error-detail-view">
        <el-row :gutter="20">
          <!-- 左侧：详细信息 -->
          <el-col :span="14">
            <el-descriptions title="错误数据日志详情" :column="2" border>
              <el-descriptions-item label="场库名称" label-align="right" label-class-name="desc-label" :span="2">
                {{ form.parkingName || '--' }}
              </el-descriptions-item>
              <el-descriptions-item label="车牌号" label-align="right" label-class-name="desc-label">
                <el-tag v-if="form.plateNum" :type="getPlateNoTagType(form.plateNum)" :color="getPlateNoColor(form.plateNum)" effect="plain">
                  {{ form.plateNum }}
                </el-tag>
                <span v-else>--</span>
              </el-descriptions-item>
              <el-descriptions-item label="错误类型" label-align="right" label-class-name="desc-label">
                <dict-tag :options="error_data_code" :value="form.errCode" />
              </el-descriptions-item>
              <el-descriptions-item label="入场时间" label-align="right" label-class-name="desc-label">
                {{ form.inTime || '--' }}
              </el-descriptions-item>
              <el-descriptions-item label="出场时间" label-align="right" label-class-name="desc-label">
                {{ form.outTime || '--' }}
              </el-descriptions-item>
              <el-descriptions-item label="入场通道" label-align="right" label-class-name="desc-label">
                {{ form.inChannelName || '--' }}
              </el-descriptions-item>
              <el-descriptions-item label="出场通道" label-align="right" label-class-name="desc-label">
                {{ form.outChannelName || '--' }}
              </el-descriptions-item>
              <el-descriptions-item label="金额" label-align="right" label-class-name="desc-label">
                <span v-if="form.money !== null && form.money !== undefined" style="color: #f56c6c; font-weight: bold;">
                  ¥{{ form.money }}
                </span>
                <span v-else>--</span>
              </el-descriptions-item>
              <el-descriptions-item label="更新时间" label-align="right" label-class-name="desc-label">
                {{ form.lastUpdate ? parseTime(form.lastUpdate, '{y}-{m}-{d} {h}:{i}:{s}') : '--' }}
              </el-descriptions-item>
              <el-descriptions-item label="备注" label-align="right" label-class-name="desc-label" :span="2">
                {{ form.remark || '--' }}
              </el-descriptions-item>
            </el-descriptions>
          </el-col>

          <!-- 右侧：图片显示 -->
          <el-col :span="10">
            <div class="image-section">
              <h3 style="margin-bottom: 15px; color: #303133;">相关图片</h3>
              <div class="image-container">
                <div v-if="form.imgPath" class="image-item">
                  <el-image :src="form.imgPath" fit="cover" style="width: 100%; height: 300px; border-radius: 4px;"
                    :preview-src-list="[form.imgPath]" preview-teleported />
                </div>
                <div v-else class="no-image">
                  <el-empty description="暂无图片" />
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 编辑模式：表单（已注释，只保留查看功能） -->
      <!-- <el-form v-else ref="errorDataLogRef" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="车牌号" prop="plateNum">
              <el-input v-model="form.plateNum" placeholder="请输入车牌号" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="场库名称" prop="parkingId">
              <el-select v-model="form.parkingId" placeholder="请选择场库">
                <el-option
                  v-for="warehouse in warehouseOptions"
                  :key="warehouse.id"
                  :label="warehouse.warehouseName"
                  :value="warehouse.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="错误类型" prop="errCode">
              <el-select v-model="form.errCode" placeholder="请选择错误类型">
                <el-option
                  v-for="dict in error_data_code"
                  :key="dict.value"
                  :label="dict.label"
                  :value="parseInt(dict.value)"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="金额" prop="money">
              <el-input v-model="form.money" placeholder="请输入金额">
                <template #prepend>¥</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="入场时间" prop="inTime">
              <el-input v-model="form.inTime" placeholder="请输入入场时间" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="出场时间" prop="outTime">
              <el-input v-model="form.outTime" placeholder="请输入出场时间" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="入场通道" prop="inChannelName">
              <el-input v-model="form.inChannelName" placeholder="请输入入场通道" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="出场通道" prop="outChannelName">
              <el-input v-model="form.outChannelName" placeholder="请输入出场通道" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="图片路径" prop="imgPath">
              <el-input v-model="form.imgPath" placeholder="请输入图片路径" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input v-model="form.remark" type="textarea" :rows="4" placeholder="请输入备注信息" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form> -->
      <template #footer>
        <div class="dialog-footer">
          <!-- <el-button v-if="!isViewMode" type="primary" @click="submitForm">确 定</el-button> -->
          <el-button @click="cancel">关 闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="ErrorDataLog">
import { ref, reactive, toRefs, getCurrentInstance, onMounted } from 'vue';
import { parseTime } from "@/utils/ruoyi";
import { optionSelectWarehouse } from "@/api/platform/warehouse";
import { getWarehouseOptions } from "@/api/vip/member";
import {
  listErrorDataLog,
  getErrorDataLog,
  // addErrorDataLog,
  // updateErrorDataLog,
  delErrorDataLog,
  countErrorDataLogByErrCode,
  countErrorDataLogByParking,
  exportErrorDataLog
} from "@/api/order/exceptionOrder";
import CustomPagination from "@/components/CustomPagination/index.vue";

// 注册组件
defineOptions({
  components: {
    CustomPagination,
  },
});

const { proxy } = getCurrentInstance();
const { error_data_code } = proxy.useDict("error_data_code");

const errorDataLogList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const updateDate = ref('');
const warehouseOptions = ref([]);
const warehouseCascaderOptions = ref([]);
const isViewMode = ref(false); // 是否为查看模式

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    plateNum: null,
    parkingId: null,
    errCode: null,
    beginTime: null,
    endTime: null
  },
  rules: {
    plateNum: [
      { required: true, message: "车牌号不能为空", trigger: "blur" }
    ],
    parkingId: [
      { required: true, message: "场库ID不能为空", trigger: "change" }
    ],
    errCode: [
      { required: true, message: "错误类型不能为空", trigger: "change" }
    ]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询错误数据日志列表 */
function getList() {
  loading.value = true;
  queryParams.value.params = {};

  // 处理更新时间（单日期查询）
  if (updateDate.value) {
    queryParams.value.params["beginTime"] = updateDate.value + ' 00:00:00';
    queryParams.value.params["endTime"] = updateDate.value + ' 23:59:59';
  } else {
    queryParams.value.params["beginTime"] = null;
    queryParams.value.params["endTime"] = null;
  }
  listErrorDataLog(queryParams.value).then(response => {
    errorDataLogList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  updateDate.value = '';
  proxy.resetForm("queryRef");
  // 重置查询参数到初始状态
  Object.assign(queryParams.value, {
    pageNum: 1,
    pageSize: 10,
    plateNum: null,
    parkingId: null,
    errCode: null,
    beginTime: null,
    endTime: null
  });
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 修改按钮操作（已注释，只保留查看功能） */
// function handleUpdate(row) {
//   reset();
//   isViewMode.value = false; // 设置为编辑模式
//   const id = row.id || ids.value[0];
//   getErrorDataLog(id).then(response => {
//     form.value = response.data;
//     open.value = true;
//     title.value = "修改错误数据日志";
//   });
// }

/** 查看按钮操作 */
function handleView(row) {
  reset();
  isViewMode.value = true; // 设置为查看模式
  form.value = { ...row };
  open.value = true;
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    "system/errorDataLog/export",
    {
      ...queryParams.value,
    },
    `错误数据日志_${new Date().getTime()}.xlsx`
  );
}

// 表单重置
function reset() {
  form.value = {};
  isViewMode.value = true; // 默认为查看模式
  // proxy.resetForm("errorDataLogRef"); // 已注释，因为不再有编辑表单
}

/** 提交按钮（已注释，只保留查看功能） */
// function submitForm() {
//   // 查看模式下不允许提交
//   if (isViewMode.value) {
//     return;
//   }

//   proxy.$refs["errorDataLogRef"].validate((valid) => {
//     if (valid) {
//       if (form.value.id != null) {
//         updateErrorDataLog(form.value).then(response => {
//           proxy.$modal.msgSuccess("修改成功");
//           open.value = false;
//           getList();
//         });
//       } else {
//         addErrorDataLog(form.value).then(response => {
//           proxy.$modal.msgSuccess("新增成功");
//           open.value = false;
//           getList();
//         });
//       }
//     }
//   });
// }

/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}

/** 新增按钮操作（已注释，只保留查看功能） */
// function handleAdd() {
//   reset();
//   open.value = true;
//   title.value = "添加错误数据日志";
// }

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除记录ID为"' + _ids + '"的数据项？').then(function() {
    return delErrorDataLog(_ids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}





/** 获取场库列表 */
function getWarehouseList() {
  getWarehouseOptions().then(response => {
    warehouseOptions.value = response.data || [];
    // 同时构建层级选项（用于cascader）
    warehouseCascaderOptions.value = buildWarehouseCascaderOptions(response.data);
  });
}

/** 构建级联选择器选项 */
function buildWarehouseCascaderOptions(warehouses) {
  if (!warehouses || warehouses.length === 0) {
    return [];
  }

  // 分离主场库和子场库
  const mainWarehouses = warehouses.filter(w => w.parentId === "0");
  const subWarehouses = warehouses.filter(w => w.parentId !== "0");

  // 构建级联结构
  return mainWarehouses.map(mainWarehouse => {
    const children = subWarehouses
      .filter(sub => sub.parentId === mainWarehouse.id)
      .map(sub => ({
        value: sub.id,
        label: sub.warehouseName,
        isLeaf: true
      }));

    return {
      value: mainWarehouse.id,
      label: mainWarehouse.warehouseName,
      children: children.length > 0 ? children : undefined
    };
  });
}

/** 判断是否为主场库 */
function isMainWarehouse(warehouse) {
  return warehouse && warehouse.parentId === "0";
}

/** 根据目标ID构建完整的场库路径 */
function buildWarehousePath(targetId, warehouses) {
  if (!targetId || !warehouses) {
    return null;
  }

  // 转换为字符串进行比较
  targetId = String(targetId);

  // 查找当前场库
  const currentWarehouse = warehouses.find(w => String(w.id) === targetId);
  if (!currentWarehouse) {
    return null;
  }

  // 如果是主场库，直接返回ID
  if (isMainWarehouse(currentWarehouse)) {
    return [currentWarehouse.id];
  }

  // 如果是子场库，需要找到父场库
  const parentWarehouse = warehouses.find(w => String(w.id) === String(currentWarehouse.parentId));
  if (parentWarehouse) {
    return [parentWarehouse.id, currentWarehouse.id];
  }

  // 如果找不到父场库，返回当前场库ID
  return [currentWarehouse.id];
}

// 获取车牌号标签类型
function getPlateNoTagType(plateNo) {
  if (!plateNo) return 'info';
  // 8位为新能源车牌，7位为普通车牌
  return plateNo.length === 8 ? 'success' : 'primary';
}

// 获取车牌号颜色
function getPlateNoColor(plateNo) {
  if (!plateNo) return '#909399';
  // 新能源车牌：浅绿色，普通车牌：浅蓝色
  return plateNo.length === 8 ? '#d4edda' : '#cce7ff';
}

// 获取车牌类型
function getPlateType(plateNo) {
  if (!plateNo) return 1;
  // 8位为新能源车牌(2)，7位为普通车牌(1)
  return plateNo.length === 8 ? 2 : 1;
}



onMounted(() => {
  getList();
  getWarehouseList();
});
</script>

<style scoped>
/* 查看模式容器样式 */
.error-detail-view {
  padding: 20px;
}

/* 图片区域样式 */
.image-section {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.image-section h3 {
  margin: 0 0 15px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.image-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.image-item {
  text-align: center;
}

.no-image {
  text-align: center;
  padding: 40px 0;
  background: #fff;
  border-radius: 4px;
  border: 1px dashed #dcdfe6;
}

/* 描述列表样式优化 */
:deep(.el-descriptions__title) {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 20px;
}

:deep(.el-descriptions__body .el-descriptions__table .el-descriptions__cell) {
  padding: 12px 16px;
}

:deep(.el-descriptions__label) {
  font-weight: 500;
  color: #606266;
  width: 90px !important;
  min-width: 90px !important;
  max-width: 90px !important;
  text-align: right !important;
}

:deep(.el-descriptions__content) {
  color: #303133;
}

/* 标签列宽度统一 */
:deep(.desc-label) {
  width: 90px !important;
  min-width: 90px !important;
  max-width: 90px !important;
  text-align: right !important;
}

/* 强制描述列表标签列宽度一致 */
:deep(.el-descriptions .el-descriptions__label.desc-label) {
  width: 90px !important;
  min-width: 90px !important;
  max-width: 90px !important;
  text-align: right !important;
  box-sizing: border-box !important;
}

:deep(.el-descriptions__body .el-descriptions__table .el-descriptions__cell.el-descriptions__label) {
  width: 90px !important;
  min-width: 90px !important;
  max-width: 90px !important;
  text-align: right !important;
}

.money-text {
  color: #f56c6c;
  font-weight: bold;
}

/* 错误类型标签包装器 */
.error-type-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  min-height: 32px;
}

/* 优化 dict-tag 样式 */
:deep(.dict-tag) {
  display: inline-block;
  vertical-align: middle;
}

/* 表格单元格对齐优化 */
:deep(.el-table .el-table__cell) {
  vertical-align: middle;
}

/* 表格标签元素居中对齐 */
:deep(.el-table .el-tag) {
  vertical-align: middle;
}

/* 通用样式 */
.text-gray-400 {
  color: #9ca3af;
}

.el-table .cell {
  padding: 0 8px;
}

.el-form-item {
  margin-bottom: 15px;
}

.mb8 {
  margin-bottom: 8px;
}

.app-container {
  padding: 20px;
}

.el-table {
  border-radius: 4px;
  overflow: hidden;
}

.el-table th {
  background-color: #fafafa;
  color: #606266;
  font-weight: 500;
}

.el-table td {
  border-bottom: 1px solid #ebeef5;
}

.el-table tr:hover td {
  background-color: #f5f7fa;
}

.el-tag {
  border-radius: 12px;
  font-size: 12px;
  padding: 0 8px;
  height: 24px;
  line-height: 22px;
}

.el-button--text {
  padding: 0;
  margin-right: 8px;
}

.el-button--text:last-child {
  margin-right: 0;
}

.fixed-width {
  width: 150px;
}

.small-padding .cell {
  padding-left: 8px;
  padding-right: 8px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .error-detail-view .el-col:first-child {
    margin-bottom: 20px;
  }
}
</style>
