2025-07-28 14:17:04.874  INFO 43780 --- [main] com.lgjy.wx.LgjyWxApplication            : The following 1 profile is active: "dev"
2025-07-28 14:17:06.414  INFO 43780 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-28 14:17:06.434  INFO 43780 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-28 14:17:06.505  INFO 43780 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 17 ms. Found 0 Redis repository interfaces.
2025-07-28 14:17:06.938  INFO 43780 --- [main] o.s.cloud.context.scope.GenericScope     : BeanFactory id=164fa476-244e-3df1-8de0-03b9eafc3092
2025-07-28 14:17:08.246  INFO 43780 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 9206 (http)
2025-07-28 14:17:08.273  INFO 43780 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-07-28 14:17:08.274  INFO 43780 --- [main] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.105]
2025-07-28 14:17:08.314  WARN 43780 --- [main] o.a.c.webresources.DirResourceSet        : Disabled the global canonical file name cache to protect against CVE-2024-56337 when starting the WebResourceSet at [C:\Users\<USER>\AppData\Local\Temp\tomcat-docbase.9206.7223678820465883158] which is part of the web application []
2025-07-28 14:17:08.648  INFO 43780 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-07-28 14:17:08.648  INFO 43780 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 3720 ms
2025-07-28 14:17:08.839  INFO 43780 --- [main] c.a.d.s.b.a.DruidDataSourceAutoConfigure : Init DruidDataSource
2025-07-28 14:17:09.532  INFO 43780 --- [main] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1} inited
2025-07-28 14:17:10.705  INFO 43780 --- [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'park-gate' URL not provided. Will try picking an instance via load-balancing.
2025-07-28 14:17:10.985  INFO 43780 --- [main] c.a.c.sentinel.SentinelWebMvcConfigurer  : [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
2025-07-28 14:17:13.443  INFO 43780 --- [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'park-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-28 14:17:16.033  WARN 43780 --- [main] iguration$LoadBalancerCaffeineWarnLogger : Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-07-28 14:17:16.044  INFO 43780 --- [main] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 1 endpoint(s) beneath base path '/actuator'
2025-07-28 14:17:16.168  INFO 43780 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 9206 (http) with context path ''
2025-07-28 14:17:16.183  INFO 43780 --- [main] c.a.n.p.a.s.c.ClientAuthPluginManager    : [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-28 14:17:16.184  INFO 43780 --- [main] c.a.n.p.a.s.c.ClientAuthPluginManager    : [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-28 14:17:16.380  INFO 43780 --- [main] c.a.c.n.registry.NacosServiceRegistry    : nacos registry, DEFAULT_GROUP park-wx *************:9206 register finished
2025-07-28 14:17:17.511  INFO 43780 --- [scheduling-1] com.lgjy.wx.task.OrderTimeoutTask        : 开始执行订单超时处理任务
2025-07-28 14:17:17.513  INFO 43780 --- [main] com.lgjy.wx.LgjyWxApplication            : Started LgjyWxApplication in 20.005 seconds (JVM running for 21.623)
2025-07-28 14:17:17.534  INFO 43780 --- [main] c.a.c.n.refresh.NacosContextRefresher    : [Nacos Config] Listening config: dataId=park-wx.yml, group=DEFAULT_GROUP
2025-07-28 14:17:17.535  INFO 43780 --- [main] c.a.c.n.refresh.NacosContextRefresher    : [Nacos Config] Listening config: dataId=park-wx, group=DEFAULT_GROUP
2025-07-28 14:17:17.540  INFO 43780 --- [main] c.a.c.n.refresh.NacosContextRefresher    : [Nacos Config] Listening config: dataId=common-dev.yml, group=DEFAULT_GROUP
2025-07-28 14:17:17.543  INFO 43780 --- [main] c.a.c.n.refresh.NacosContextRefresher    : [Nacos Config] Listening config: dataId=park-wx-dev.yml, group=DEFAULT_GROUP
2025-07-28 14:17:17.647 ERROR 43780 --- [scheduling-1] com.lgjy.wx.task.OrderTimeoutTask        : 订单超时处理任务执行失败

org.springframework.jdbc.BadSqlGrammarException: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Unknown column 'operate_type' in 'field list'
### The error may exist in file [F:\parking\park-api\lgjy-modules\lgjy-wx\target\classes\mapper\wx\WxPackageMapper.xml]
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: select id, warehouse_id, package_id, user_id, phone_number, plate_no, vip_type,                operate_type, transact_time, begin_vip_time, expiration_time, payment_amount, discount_amount, actual_payment,                trade_id, pay_status, invoice_id, parking_space_no, group_buy_record_id,                choose_time, delete_flag, create_by, create_time, update_by, update_time from mini_vip_transact_record               where create_time   <=   ?           and pay_status = ?           and delete_flag = 0         order by create_time asc         limit 100
### Cause: java.sql.SQLSyntaxErrorException: Unknown column 'operate_type' in 'field list'
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Unknown column 'operate_type' in 'field list'
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:236) ~[spring-jdbc-5.3.39.jar:5.3.39]
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:73) ~[spring-jdbc-5.3.39.jar:5.3.39]
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:92) ~[mybatis-spring-2.1.1.jar:2.1.1]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:439) ~[mybatis-spring-2.1.1.jar:2.1.1]
	at com.sun.proxy.$Proxy122.selectList(Unknown Source) ~[na:na]
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224) ~[mybatis-spring-2.1.1.jar:2.1.1]
	at org.apache.ibatis.binding.MapperMethod.executeForMany(MapperMethod.java:147) ~[mybatis-3.5.13.jar:3.5.13]
	at org.apache.ibatis.binding.MapperMethod.execute(MapperMethod.java:80) ~[mybatis-3.5.13.jar:3.5.13]
	at org.apache.ibatis.binding.MapperProxy$PlainMethodInvoker.invoke(MapperProxy.java:142) ~[mybatis-3.5.13.jar:3.5.13]
	at org.apache.ibatis.binding.MapperProxy.invoke(MapperProxy.java:86) ~[mybatis-3.5.13.jar:3.5.13]
	at com.sun.proxy.$Proxy133.selectTimeoutOrders(Unknown Source) ~[na:na]
	at com.lgjy.wx.task.OrderTimeoutTask.processTimeoutOrders(OrderTimeoutTask.java:38) ~[classes/:na]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_452]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_452]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_452]
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84) [spring-context-5.3.39.jar:5.3.39]
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54) [spring-context-5.3.39.jar:5.3.39]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511) [na:1.8.0_452]
	at java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:308) [na:1.8.0_452]
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java) [na:1.8.0_452]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180) [na:1.8.0_452]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294) [na:1.8.0_452]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) [na:1.8.0_452]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) [na:1.8.0_452]
	at java.lang.Thread.run(Thread.java:750) [na:1.8.0_452]
Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'operate_type' in 'field list'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:483) ~[druid-1.2.25.jar:na]
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:65) ~[mybatis-3.5.13.jar:3.5.13]
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:80) ~[mybatis-3.5.13.jar:3.5.13]
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:65) ~[mybatis-3.5.13.jar:3.5.13]
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:333) ~[mybatis-3.5.13.jar:3.5.13]
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:158) ~[mybatis-3.5.13.jar:3.5.13]
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:110) ~[mybatis-3.5.13.jar:3.5.13]
	at com.github.pagehelper.PageInterceptor.intercept(PageInterceptor.java:169) ~[pagehelper-6.0.0.jar:na]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.13.jar:3.5.13]
	at com.sun.proxy.$Proxy201.query(Unknown Source) ~[na:na]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:154) ~[mybatis-3.5.13.jar:3.5.13]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147) ~[mybatis-3.5.13.jar:3.5.13]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:142) ~[mybatis-3.5.13.jar:3.5.13]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_452]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_452]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_452]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425) ~[mybatis-spring-2.1.1.jar:2.1.1]
	... 22 common frames omitted

2025-07-28 14:17:19.305  INFO 43780 --- [RMI TCP Connection(3)-*************] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-28 14:17:19.305  INFO 43780 --- [RMI TCP Connection(3)-*************] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-07-28 14:17:19.307  INFO 43780 --- [RMI TCP Connection(3)-*************] o.s.web.servlet.DispatcherServlet        : Completed initialization in 2 ms
