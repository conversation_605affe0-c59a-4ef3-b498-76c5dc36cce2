2025-07-28 14:08:50.250  INFO 29968 --- [main] com.lgjy.system.LgjySystemApplication    : The following 1 profile is active: "dev"
2025-07-28 14:08:51.098  INFO 29968 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-28 14:08:51.100  INFO 29968 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-28 14:08:51.127  INFO 29968 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 15 ms. Found 0 Redis repository interfaces.
2025-07-28 14:08:51.318  INFO 29968 --- [main] o.s.cloud.context.scope.GenericScope     : BeanFactory id=20a971a3-cb48-330f-ad21-a538a4b7d629
2025-07-28 14:08:51.872  INFO 29968 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 9201 (http)
2025-07-28 14:08:51.885  INFO 29968 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-07-28 14:08:51.885  INFO 29968 --- [main] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.105]
2025-07-28 14:08:51.904  WARN 29968 --- [main] o.a.c.webresources.DirResourceSet        : Disabled the global canonical file name cache to protect against CVE-2024-56337 when starting the WebResourceSet at [C:\Users\<USER>\AppData\Local\Temp\tomcat-docbase.9201.7089872142887675887] which is part of the web application []
2025-07-28 14:08:52.018  INFO 29968 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-07-28 14:08:52.018  INFO 29968 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1745 ms
2025-07-28 14:08:52.118  INFO 29968 --- [main] c.a.d.s.b.a.DruidDataSourceAutoConfigure : Init DruidDataSource
2025-07-28 14:08:52.611  INFO 29968 --- [main] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1} inited
2025-07-28 14:08:53.766  INFO 29968 --- [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'park-gate' URL not provided. Will try picking an instance via load-balancing.
2025-07-28 14:08:53.875  INFO 29968 --- [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'park-wx' URL not provided. Will try picking an instance via load-balancing.
2025-07-28 14:08:53.966  INFO 29968 --- [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'park-wx' URL not provided. Will try picking an instance via load-balancing.
2025-07-28 14:08:56.156  INFO 29968 --- [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'park-file' URL not provided. Will try picking an instance via load-balancing.
2025-07-28 14:08:56.395  INFO 29968 --- [main] c.a.c.sentinel.SentinelWebMvcConfigurer  : [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
2025-07-28 14:08:58.738  INFO 29968 --- [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'park-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-28 14:09:00.164  WARN 29968 --- [main] iguration$LoadBalancerCaffeineWarnLogger : Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-07-28 14:09:00.172  INFO 29968 --- [main] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 1 endpoint(s) beneath base path '/actuator'
2025-07-28 14:09:00.252  INFO 29968 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 9201 (http) with context path ''
2025-07-28 14:09:00.262  INFO 29968 --- [main] c.a.n.p.a.s.c.ClientAuthPluginManager    : [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-28 14:09:00.262  INFO 29968 --- [main] c.a.n.p.a.s.c.ClientAuthPluginManager    : [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-28 14:09:00.449  INFO 29968 --- [main] c.a.c.n.registry.NacosServiceRegistry    : nacos registry, DEFAULT_GROUP park-system *************:9201 register finished
2025-07-28 14:09:01.545  INFO 29968 --- [main] com.lgjy.system.LgjySystemApplication    : Started LgjySystemApplication in 17.315 seconds (JVM running for 18.787)
2025-07-28 14:09:01.558  INFO 29968 --- [main] c.a.c.n.refresh.NacosContextRefresher    : [Nacos Config] Listening config: dataId=park-system.yml, group=DEFAULT_GROUP
2025-07-28 14:09:01.559  INFO 29968 --- [main] c.a.c.n.refresh.NacosContextRefresher    : [Nacos Config] Listening config: dataId=park-system, group=DEFAULT_GROUP
2025-07-28 14:09:01.561  INFO 29968 --- [main] c.a.c.n.refresh.NacosContextRefresher    : [Nacos Config] Listening config: dataId=common-dev.yml, group=DEFAULT_GROUP
2025-07-28 14:09:01.562  INFO 29968 --- [main] c.a.c.n.refresh.NacosContextRefresher    : [Nacos Config] Listening config: dataId=park-system-dev.yml, group=DEFAULT_GROUP
2025-07-28 14:09:02.446  INFO 29968 --- [RMI TCP Connection(2)-*************] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-28 14:09:02.447  INFO 29968 --- [RMI TCP Connection(2)-*************] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-07-28 14:09:02.450  INFO 29968 --- [RMI TCP Connection(2)-*************] o.s.web.servlet.DispatcherServlet        : Completed initialization in 3 ms
2025-07-28 14:16:03.261  WARN 29968 --- [Thread-2] c.a.n.common.http.HttpClientBeanHolder   : [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-28 14:16:03.262  WARN 29968 --- [Thread-6] c.a.nacos.common.notify.NotifyCenter     : [NotifyCenter] Start destroying Publisher
2025-07-28 14:16:03.262  WARN 29968 --- [Thread-6] c.a.nacos.common.notify.NotifyCenter     : [NotifyCenter] Destruction of the end
2025-07-28 14:16:03.263  WARN 29968 --- [Thread-2] c.a.n.common.http.HttpClientBeanHolder   : [HttpClientBeanHolder] Destruction of the end
2025-07-28 14:16:04.809  INFO 29968 --- [SpringApplicationShutdownHook] c.a.c.n.registry.NacosServiceRegistry    : De-registering from Nacos Server now...
2025-07-28 14:16:04.813  INFO 29968 --- [SpringApplicationShutdownHook] c.a.c.n.registry.NacosServiceRegistry    : De-registration finished.
2025-07-28 14:16:04.959  INFO 29968 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1} closing ...
2025-07-28 14:16:04.968  INFO 29968 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1} closed
