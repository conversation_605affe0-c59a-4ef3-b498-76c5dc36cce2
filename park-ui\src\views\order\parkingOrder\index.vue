<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="车牌号" prop="plateNo">
        <el-input
          v-model="queryParams.plateNo"
          placeholder="请输入车牌号"
          clearable
          style="width: 180px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="场库名称" prop="warehouseId">
        <el-select
          v-model="queryParams.warehouseId"
          placeholder="请选择场库"
          clearable
          style="width: 200px"
        >
          <el-option
            v-for="warehouse in warehouseOptions"
            :key="warehouse.id"
            :label="warehouse.warehouseName"
            :value="warehouse.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="支付状态" prop="payStatus">
        <el-select
          v-model="queryParams.payStatus"
          placeholder="请选择支付状态"
          clearable
          style="width: 200px"
        >
          <el-option
            v-for="dict in pay_status"
            :key="dict.value"
            :label="dict.label"
            :value="parseInt(dict.value)"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="支付方式" prop="payType">
        <el-select
          v-model="queryParams.payType"
          placeholder="请选择支付方式"
          clearable
          style="width: 200px"
        >
          <el-option
            v-for="dict in pay_method"
            :key="dict.value"
            :label="dict.label"
            :value="parseInt(dict.value)"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="车辆类型" prop="carType">
        <el-select
          v-model="queryParams.carType"
          placeholder="请选择车辆类型"
          clearable
          style="width: 200px"
        >
          <el-option
            v-for="carType in carTypeOptions"
            :key="carType"
            :label="carType"
            :value="carType"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="停车时间">
        <el-date-picker
          v-model="dateRange"
          style="width: 240px"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <!-- <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['order:parkingOrder:add']"
        >新增</el-button>
      </el-col> -->
      <!-- <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['order:parkingOrder:edit']"
        >修改</el-button>
      </el-col> -->
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['order:parkingOrder:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['order:parkingOrder:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="parkingOrderList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="订单号" align="center" prop="tradeId" width="180">
        <template #default="scope">
          <span>{{ scope.row.tradeId || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="场库名称" align="center" prop="warehouseName" width="100">
        <template #default="scope">
          <span>{{ scope.row.warehouseName || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="车牌号" align="center" prop="plateNo" width="120">
        <template #default="scope">
          <el-tag
            v-if="scope.row.plateNo"
            :type="getPlateNoTagType(scope.row.plateNo)"
            :color="getPlateNoColor(scope.row.plateNo)"
            effect="plain"
          >
            {{ scope.row.plateNo }}
          </el-tag>
          <span v-else>--</span>
        </template>
      </el-table-column>
      <el-table-column label="车辆类型" align="center" prop="carType" width="100">
        <template #default="scope">
          <el-tag v-if="scope.row.carType" type="primary">{{ scope.row.carType }}</el-tag>
          <span v-else>--</span>
        </template>
      </el-table-column>
      <el-table-column label="停车时长" align="center" width="100">
        <template #default="scope">
          <span v-if="scope.row.parkingDuration">
            {{ formatDuration(scope.row.parkingDuration) }}
          </span>
          <span v-else>--</span>
        </template>
      </el-table-column>
      <el-table-column label="应付金额" align="center" prop="paymentAmount" width="100">
        <template #default="scope">
          <span v-if="scope.row.paymentAmount !== null && scope.row.paymentAmount !== undefined" class="amount-text">¥{{ scope.row.paymentAmount }}</span>
          <span v-else>--</span>
        </template>
      </el-table-column>
      <el-table-column label="优惠金额" align="center" prop="discountAmount" width="100">
        <template #default="scope">
          <span v-if="scope.row.discountAmount !== null && scope.row.discountAmount !== undefined" class="discount-text">¥{{ scope.row.discountAmount }}</span>
          <span v-else>--</span>
        </template>
      </el-table-column>
      <el-table-column label="实付金额" align="center" prop="actualPayment" width="100">
        <template #default="scope">
          <span v-if="scope.row.actualPayment !== null && scope.row.actualPayment !== undefined" class="actual-payment-text">¥{{ scope.row.actualPayment }}</span>
          <span v-else>--</span>
        </template>
      </el-table-column>
      <el-table-column label="支付方式" align="center" prop="payType" width="100">
        <template #default="scope">
          <dict-tag :options="pay_method" :value="scope.row.payType"/>
        </template>
      </el-table-column>
      <el-table-column label="支付状态" align="center" prop="payStatus" width="100">
        <template #default="scope">
          <dict-tag :options="pay_status" :value="scope.row.payStatus"/>
        </template>
      </el-table-column>
      <el-table-column label="开始停车时间" align="center" prop="beginParkingTime" width="160">
        <template #default="scope">
          <span v-if="scope.row.beginParkingTime">{{ parseTime(scope.row.beginParkingTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          <span v-else>--</span>
        </template>
      </el-table-column>
      <el-table-column label="结束停车时间" align="center" prop="endParkingTime" width="160">
        <template #default="scope">
          <span v-if="scope.row.endParkingTime">{{ parseTime(scope.row.endParkingTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          <span v-else>--</span>
        </template>
      </el-table-column>
      <el-table-column label="支付时间" align="center" prop="paymentTime" width="160">
        <template #default="scope">
          <span v-if="scope.row.paymentTime">{{ parseTime(scope.row.paymentTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          <span v-else>--</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" fixed="right" width="180">
        <template #default="scope">
          <el-button
            link
            type="primary"
            icon="View"
            @click="handleView(scope.row)"
            v-hasPermi="['order:parkingOrder:query']"
          >查看</el-button>
          <el-button
            link
            type="danger"
            icon="RefreshLeft"
            @click="handleRefund(scope.row)"
            v-hasPermi="['order:parkingOrder:refund']"
            v-if="scope.row.payStatus === 3 || scope.row.payStatus === 5"
          >退款</el-button>
          <!-- <el-button
            link
            type="primary"
            icon="Edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['order:parkingOrder:edit']"
          >修改</el-button> -->
          <el-button
            link
            type="primary"
            icon="Delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['order:parkingOrder:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <custom-pagination
      :total="total"
      v-model:current-page="queryParams.pageNum"
      v-model:page-size="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 停车订单对话框 -->
    <el-dialog :title="title" v-model="open" width="900px" append-to-body>
      <!-- 查看模式：简化展示 -->
      <div v-if="isViewMode" class="order-detail-view">
        <el-card class="detail-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span class="header-title">订单详情</span>
              <el-tag type="primary" size="small">订单号：{{ form.tradeId || '--' }}</el-tag>
            </div>
          </template>

          <el-descriptions :column="2" border>
            <!-- 基本信息 -->
            <el-descriptions-item label="车牌号">
              <el-tag
                v-if="form.plateNo"
                :type="getPlateNoTagType(form.plateNo)"
                :color="getPlateNoColor(form.plateNo)"
                effect="plain"
              >
                {{ form.plateNo }}
              </el-tag>
              <span v-else>--</span>
            </el-descriptions-item>
            <el-descriptions-item label="场库名称">
              {{ form.warehouseName || '--' }}
            </el-descriptions-item>
            <el-descriptions-item label="车辆类型">
              <el-tag v-if="form.carType" type="primary">{{ form.carType }}</el-tag>
              <span v-else>--</span>
            </el-descriptions-item>
            <el-descriptions-item label="停车时长">
              <span v-if="form.parkingDuration" class="duration-text">{{ formatDuration(form.parkingDuration) }}</span>
              <span v-else>--</span>
            </el-descriptions-item>

            <!-- 金额信息 -->
            <el-descriptions-item label="应付金额">
              <span v-if="form.paymentAmount !== null && form.paymentAmount !== undefined" class="amount-text">¥{{ form.paymentAmount }}</span>
              <span v-else>--</span>
            </el-descriptions-item>
            <el-descriptions-item label="优惠金额">
              <span v-if="form.discountAmount !== null && form.discountAmount !== undefined" class="discount-text">¥{{ form.discountAmount }}</span>
              <span v-else>--</span>
            </el-descriptions-item>
            <el-descriptions-item label="实付金额">
              <span v-if="form.actualPayment !== null && form.actualPayment !== undefined" class="actual-payment-text">¥{{ form.actualPayment }}</span>
              <span v-else>--</span>
            </el-descriptions-item>
            <el-descriptions-item label="支付方式">
              <dict-tag :options="pay_method" :value="form.payType"/>
            </el-descriptions-item>
            <el-descriptions-item label="支付状态">
              <dict-tag :options="pay_status" :value="form.payStatus"/>
            </el-descriptions-item>
            <el-descriptions-item label="支付时间">
              {{ form.paymentTime ? parseTime(form.paymentTime, '{y}-{m}-{d} {h}:{i}:{s}') : '--' }}
            </el-descriptions-item>
            <!-- 时间信息 -->
            <el-descriptions-item label="开始停车时间">
              {{ form.beginParkingTime ? parseTime(form.beginParkingTime, '{y}-{m}-{d} {h}:{i}:{s}') : '--' }}
            </el-descriptions-item>
            <el-descriptions-item label="结束停车时间">
              {{ form.endParkingTime ? parseTime(form.endParkingTime, '{y}-{m}-{d} {h}:{i}:{s}') : '--' }}
            </el-descriptions-item>
            
          </el-descriptions>
        </el-card>
      </div>

      <!-- 编辑模式：表单 -->
      <el-form v-else ref="orderRef" :model="form" :rules="rules" label-width="120px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="车牌号" prop="plateNo">
              <el-input v-model="form.plateNo" placeholder="请输入车牌号" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="场库名称" prop="warehouseId">
              <el-select v-model="form.warehouseId" placeholder="请选择场库" style="width: 100%">
                <el-option
                  v-for="warehouse in warehouseOptions"
                  :key="warehouse.id"
                  :label="warehouse.warehouseName"
                  :value="warehouse.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="车辆类型" prop="carType">
              <el-select v-model="form.carType" placeholder="请选择车辆类型" style="width: 100%">
                <el-option
                  v-for="carType in carTypeOptions"
                  :key="carType"
                  :label="carType"
                  :value="carType"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="支付状态" prop="payStatus">
              <el-select v-model="form.payStatus" placeholder="请选择支付状态" style="width: 100%">
                <el-option
                  v-for="dict in pay_status"
                  :key="dict.value"
                  :label="dict.label"
                  :value="parseInt(dict.value)"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="支付方式" prop="payType">
              <el-select v-model="form.payType" placeholder="请选择支付方式" style="width: 100%">
                <el-option
                  v-for="dict in pay_method"
                  :key="dict.value"
                  :label="dict.label"
                  :value="parseInt(dict.value)"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="应付金额" prop="paymentAmount">
              <el-input v-model="form.paymentAmount" placeholder="请输入应付金额">
                <template #prepend>¥</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="优惠金额" prop="discountAmount">
              <el-input v-model="form.discountAmount" placeholder="请输入优惠金额">
                <template #prepend>¥</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="实付金额" prop="actualPayment">
              <el-input v-model="form.actualPayment" placeholder="请输入实付金额">
                <template #prepend>¥</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="开始停车时间" prop="beginParkingTime">
              <el-date-picker
                v-model="form.beginParkingTime"
                type="datetime"
                placeholder="请选择开始停车时间"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="结束停车时间" prop="endParkingTime">
              <el-date-picker
                v-model="form.endParkingTime"
                type="datetime"
                placeholder="请选择结束停车时间"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="停车时长(分钟)" prop="parkingDuration">
              <el-input v-model="form.parkingDuration" placeholder="请输入停车时长" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button v-if="!isViewMode" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">{{ isViewMode ? '关 闭' : '取 消' }}</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 退款对话框 -->
    <el-dialog title="退款处理" v-model="refundOpen" width="400px" append-to-body>
      <el-form ref="refundRef" :model="refundForm" :rules="refundRules" label-width="100px">
        <el-form-item label="订单号">
          <span>{{ refundForm.tradeId }}</span>
        </el-form-item>
        <el-form-item label="实付金额">
          <span class="actual-payment-text">¥{{ refundForm.originalAmount }}</span>
        </el-form-item>
        <el-form-item label="退款金额" prop="actualPayment">
          <el-input-number
            v-model="refundForm.actualPayment"
            :min="0"
            :max="refundForm.originalAmount"
            :precision="2"
            placeholder="请输入退款金额"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="退款原因">
          <el-input
            v-model="refundForm.refundReason"
            type="textarea"
            placeholder="请输入退款原因（选填）"
            :rows="3"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitRefund">确 定</el-button>
          <el-button @click="cancelRefund">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="ParkingOrder">
import { ref, reactive, toRefs, getCurrentInstance, onMounted } from 'vue';
import { Picture } from '@element-plus/icons-vue';
import { parseTime } from "@/utils/ruoyi";
import {
  listParkingOrder,
  getParkingOrder,
  delParkingOrder,
  addParkingOrder,
  updateParkingOrder,
  getCarTypeOptions,
  refundParkingOrder
} from "@/api/order/parkingOrder";
import { optionSelectWarehouse } from "@/api/platform/warehouse";
import CustomPagination from "@/components/CustomPagination/index.vue";

// 注册组件
defineOptions({
  components: {
    CustomPagination,
  },
});

const { proxy } = getCurrentInstance();
const { pay_status, pay_method } = proxy.useDict("pay_status", "pay_method");

const parkingOrderList = ref([]);
const open = ref(false);
const refundOpen = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const dateRange = ref([]);
const warehouseOptions = ref([]);
const carTypeOptions = ref([]); // 车辆类型选项

const isViewMode = ref(false); // 是否为查看模式

const data = reactive({
  form: {},
  refundForm: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    plateNo: null,
    warehouseId: null,
    payStatus: null,
    payType: null,
    carType: null,
    beginTime: null,
    endTime: null
  },
  rules: {
    plateNo: [
      { required: true, message: "车牌号不能为空", trigger: "blur" }
    ],
    payStatus: [
      { required: true, message: "支付状态不能为空", trigger: "change" }
    ]
  },
  refundRules: {
    actualPayment: [
      { required: true, message: "退款金额不能为空", trigger: "blur" },
      { type: 'number', min: 0.01, message: "退款金额必须大于0", trigger: "blur" }
    ]
  }
});

const { queryParams, form, rules, refundForm, refundRules } = toRefs(data);

/** 查询停车订单列表 */
function getList() {
  loading.value = true;
  queryParams.value.params = {};
  if (null != dateRange.value && '' != dateRange.value) {
    queryParams.value.beginTime = dateRange.value[0];
    queryParams.value.endTime = dateRange.value[1];
  }
  listParkingOrder(queryParams.value).then(response => {
    parkingOrderList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  dateRange.value = [];
  proxy.resetForm("queryRef");
  // 重置查询参数到初始状态
  Object.assign(queryParams.value, {
    pageNum: 1,
    pageSize: 10,
    plateNo: null,
    warehouseId: null,
    payStatus: null,
    payType: null,
    carType: null,
    beginTime: null,
    endTime: null
  });
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  isViewMode.value = false; // 设置为编辑模式
  open.value = true;
  title.value = "添加停车订单";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  isViewMode.value = false; // 设置为编辑模式
  const id = row.id || ids.value[0];
  getParkingOrder(id).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改停车订单";
  });
}

/** 查看按钮操作 */
function handleView(row) {
  reset();
  isViewMode.value = true; // 设置为查看模式
  const id = row.id;
  getParkingOrder(id).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "查看停车订单详情";
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const orderIds = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除停车订单编号为"' + orderIds + '"的数据项？').then(function() {
    return delParkingOrder(orderIds);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    "system/parkingOrder/export",
    {
      ...queryParams.value,
    },
    `parking_order_${new Date().getTime()}.xlsx`
  );
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["orderRef"].validate((valid) => {
    if (valid) {
      if (form.value.id != null) {
        updateParkingOrder(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addParkingOrder(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    warehouseId: null,
    parkingManageId: null,
    userId: null,
    plateNo: null,
    beginParkingTime: null,
    endParkingTime: null,
    parkingDuration: null,
    paymentAmount: null,
    discountAmount: null,
    actualPayment: null,
    payType: null,
    parkingReservationId: null,
    invoiceId: null,
    tradeId: null,
    payStatus: 0,
    paymentTime: null,
    openId: null,
    carType: null
  };
  proxy.resetForm("orderRef");
}





// 格式化停车时长
function formatDuration(minutes) {
  if (!minutes) return '0分钟';
  const hours = Math.floor(minutes / 60);
  const mins = minutes % 60;
  if (hours > 0) {
    return mins > 0 ? `${hours}小时${mins}分钟` : `${hours}小时`;
  }
  return `${mins}分钟`;
}



/** 获取场库列表 */
function getWarehouseList() {
  optionSelectWarehouse().then(response => {
    warehouseOptions.value = response.data || [];
  });
}

/** 获取车辆类型选项 */
function getCarTypeOptionsList() {
  getCarTypeOptions().then(response => {
    carTypeOptions.value = response.data || [];
  });
}



// 获取车牌号标签类型
function getPlateNoTagType(plateNo) {
  if (!plateNo) return 'info';
  // 8位为新能源车牌，7位为普通车牌
  return plateNo.length === 8 ? 'success' : 'primary';
}

// 获取车牌号颜色
function getPlateNoColor(plateNo) {
  if (!plateNo) return '#909399';
  // 新能源车牌：浅绿色，普通车牌：浅蓝色
  return plateNo.length === 8 ? '#d4edda' : '#cce7ff';
}

// 获取车牌类型
function getPlateType(plateNo) {
  if (!plateNo) return 1;
  // 8位为新能源车牌(2)，7位为普通车牌(1)
  return plateNo.length === 8 ? 2 : 1;
}

// 格式化日期时间
function formatDateTime(dateTime) {
  if (!dateTime) return '-';
  return parseTime(dateTime, '{y}-{m}-{d} {h}:{i}:{s}');
}

/** 退款处理 */
function handleRefund(row) {
  refundForm.value = {
    id: row.id,
    tradeId: row.tradeId,
    originalAmount: row.actualPayment,
    actualPayment: row.actualPayment,
    refundReason: ''
  };
  refundOpen.value = true;
}

/** 提交退款 */
function submitRefund() {
  proxy.$refs["refundRef"].validate((valid) => {
    if (valid) {
      proxy.$modal.confirm('确认要退款 ¥' + refundForm.value.actualPayment + ' 吗？').then(() => {
        refundParkingOrder(refundForm.value).then(response => {
          proxy.$modal.msgSuccess("退款处理成功");
          refundOpen.value = false;
          getList();
        });
      });
    }
  });
}

/** 取消退款 */
function cancelRefund() {
  refundOpen.value = false;
  refundForm.value = {};
}

onMounted(() => {
  getList();
  getWarehouseList();
  getCarTypeOptionsList();
});
</script>

<style scoped>
.amount-text {
  color: #e6a23c;
  font-weight: bold;
}

.discount-text {
  color: #67c23a;
  font-weight: bold;
}

.actual-payment-text {
  color: #f56c6c;
  font-weight: bold;
  font-size: 14px;
}

.image-container {
  display: flex;
  align-items: center;
}

.image-slot {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 120px;
  height: 80px;
  background-color: #f5f7fa;
  border: 1px dashed #dcdfe6;
  border-radius: 4px;
  color: #909399;
  font-size: 12px;
}

.image-slot .el-icon {
  font-size: 24px;
  margin-bottom: 4px;
}

.image-slot-small {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100px;
  height: 60px;
  background-color: #f5f7fa;
  border: 1px dashed #dcdfe6;
  border-radius: 4px;
  color: #909399;
  font-size: 10px;
}

.image-slot-small .el-icon {
  font-size: 20px;
  margin-bottom: 2px;
}

.gate-record-item {
  margin-bottom: 16px;
}

.gate-record-item:last-child {
  margin-bottom: 0;
}

.record-count {
  color: #909399;
  font-size: 12px;
  margin-left: 8px;
}

.no-records {
  padding: 20px;
  text-align: center;
}

.el-table .cell {
  padding: 0 8px;
}

.el-form-item {
  margin-bottom: 15px;
}

.mb8 {
  margin-bottom: 8px;
}

.app-container {
  padding: 20px;
}

.el-table {
  border-radius: 4px;
  overflow: hidden;
}

.el-table th {
  background-color: #fafafa;
  color: #606266;
  font-weight: 500;
}

.el-table td {
  border-bottom: 1px solid #ebeef5;
}

.el-table tr:hover td {
  background-color: #f5f7fa;
}

.el-tag {
  border-radius: 12px;
  font-size: 12px;
  padding: 0 8px;
  height: 24px;
  line-height: 22px;
}

.el-button--text {
  padding: 0;
  margin-right: 8px;
}

.el-button--text:last-child {
  margin-right: 0;
}

.fixed-width {
  width: 180px;
}

.small-padding .cell {
  padding-left: 8px;
  padding-right: 8px;
}

/* 订单详情查看样式 */
.order-detail-view {
  padding: 0;
}

.detail-card {
  margin-bottom: 20px;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
}

.detail-card:last-child {
  margin-bottom: 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0;
}

.header-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.duration-text {
  color: #409eff;
  font-weight: 500;
}

.order-detail-view .el-descriptions {
  margin-top: 0;
}

.order-detail-view .el-descriptions__label {
  font-weight: 500;
  color: #606266;
  background-color: #fafafa;
}

.order-detail-view .el-descriptions__content {
  color: #303133;
}

.order-detail-view .amount-text {
  font-size: 16px;
  color: #e6a23c;
  font-weight: 600;
}

.order-detail-view .discount-text {
  font-size: 16px;
  color: #67c23a;
  font-weight: 600;
}

.order-detail-view .actual-payment-text {
  font-size: 16px;
  color: #f56c6c;
  font-weight: 600;
}

.order-detail-view .duration-text {
  font-size: 14px;
  color: #409eff;
  font-weight: 500;
}
</style>
