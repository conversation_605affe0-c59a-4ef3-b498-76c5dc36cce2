2025-07-28 14:04:55.324  INFO 31908 --- [nacos.client.cachedata.internal.notifier] o.s.boot.SpringApplication               : The following 1 profile is active: "dev"
2025-07-28 14:04:55.342  INFO 31908 --- [nacos.client.cachedata.internal.notifier] o.s.boot.SpringApplication               : Started application in 1.437 seconds (JVM running for 515.084)
2025-07-28 14:04:55.610  INFO 31908 --- [nacos.client.cachedata.internal.notifier] o.s.c.e.event.RefreshEventListener       : Refresh keys changed: [spring.redis.lettuce.pool.max-active, spring.cloud.gateway.routes[0].id, security.xss.excludeUrls[0], spring.redis.host, spring.cloud.gateway.routes[5].uri, security.ignore.whites[13], security.ignore.whites[0], spring.cloud.gateway.routes[3].predicates[0], spring.cloud.gateway.routes[1].id, security.ignore.whites[17], security.ignore.whites[8], security.ignore.whites[4], spring.cloud.gateway.routes[4].uri, spring.cloud.sentinel.log.dir, spring.cloud.gateway.routes[0].predicates[0], spring.cloud.gateway.routes[3].id, security.ignore.whites[10], spring.redis.port, spring.cloud.gateway.discovery.locator.lowerCaseServiceId, security.ignore.whites[14], spring.cloud.gateway.routes[0].filters[0], security.ignore.whites[18], spring.cloud.gateway.routes[1].predicates[0], spring.cloud.gateway.routes[2].filters[0], security.ignore.whites[7], spring.cloud.gateway.routes[1].filters[0], spring.cloud.gateway.routes[3].filters[0], security.ignore.whites[3], spring.cloud.gateway.routes[2].id, spring.redis.timeout, spring.cloud.gateway.routes[5].filters[0], spring.cloud.gateway.routes[2].predicates[0], spring.cloud.gateway.routes[4].filters[0], spring.redis.lettuce.pool.max-wait, spring.cloud.gateway.discovery.locator.enabled, spring.cloud.sentinel.log.switch-pid, spring.redis.lettuce.pool.max-idle, spring.cloud.gateway.routes[2].uri, security.ignore.whites[11], spring.cloud.gateway.routes[5].id, security.xss.enabled, security.ignore.whites[15], security.ignore.whites[19], spring.cloud.gateway.routes[0].filters[1], logging.file.max-history, logging.level.com.lgjy, spring.cloud.gateway.routes[0].filters[2], security.ignore.whites[6], spring.cloud.gateway.routes[1].uri, spring.cloud.gateway.routes[4].id, security.ignore.whites[2], security.captcha.type, spring.cloud.gateway.routes[4].predicates[0], spring.redis.password, spring.redis.database, logging.file.name, security.ignore.whites[12], security.ignore.whites[16], spring.cloud.gateway.routes[5].predicates[0], spring.redis.lettuce.pool.min-idle, security.ignore.whites[5], spring.cloud.gateway.routes[0].uri, security.ignore.whites[1], spring.cloud.gateway.routes[3].uri, security.captcha.enabled, logging.file.max-size, security.ignore.whites[9]]
2025-07-28 14:04:56.827  WARN 31908 --- [nacos.client.cachedata.internal.notifier] c.a.c.n.c.NacosPropertySourceBuilder     : Ignore the empty nacos configuration and get it based on dataId[park-gateway] & group[DEFAULT_GROUP]
2025-07-28 14:04:56.831  WARN 31908 --- [nacos.client.cachedata.internal.notifier] c.a.c.n.c.NacosPropertySourceBuilder     : Ignore the empty nacos configuration and get it based on dataId[park-gateway.yml] & group[DEFAULT_GROUP]
2025-07-28 14:04:56.837  INFO 31908 --- [nacos.client.cachedata.internal.notifier] b.c.PropertySourceBootstrapConfiguration : Located property source: [BootstrapPropertySource {name='bootstrapProperties-park-gateway-dev.yml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-park-gateway.yml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-park-gateway,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-common-dev.yml,DEFAULT_GROUP'}]
2025-07-28 14:04:56.851  INFO 31908 --- [nacos.client.cachedata.internal.notifier] o.s.boot.SpringApplication               : The following 1 profile is active: "dev"
2025-07-28 14:04:56.861  INFO 31908 --- [nacos.client.cachedata.internal.notifier] o.s.boot.SpringApplication               : Started application in 1.249 seconds (JVM running for 516.603)
2025-07-28 14:04:57.034  INFO 31908 --- [nacos.client.cachedata.internal.notifier] o.s.c.e.event.RefreshEventListener       : Refresh keys changed: []
2025-07-28 14:06:06.903  WARN 31908 --- [Thread-6] c.a.nacos.common.notify.NotifyCenter     : [NotifyCenter] Start destroying Publisher
2025-07-28 14:06:06.903  WARN 31908 --- [Thread-2] c.a.n.common.http.HttpClientBeanHolder   : [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-28 14:06:06.905  WARN 31908 --- [Thread-6] c.a.nacos.common.notify.NotifyCenter     : [NotifyCenter] Destruction of the end
2025-07-28 14:06:06.906  WARN 31908 --- [Thread-2] c.a.n.common.http.HttpClientBeanHolder   : [HttpClientBeanHolder] Destruction of the end
2025-07-28 14:06:06.917  INFO 31908 --- [SpringApplicationShutdownHook] c.a.c.n.registry.NacosServiceRegistry    : De-registering from Nacos Server now...
2025-07-28 14:06:06.922  INFO 31908 --- [SpringApplicationShutdownHook] c.a.c.n.registry.NacosServiceRegistry    : De-registration finished.
2025-07-28 14:06:19.145  INFO 43516 --- [main] com.lgjy.gateway.LgjyGatewayApplication  : The following 1 profile is active: "dev"
2025-07-28 14:06:19.802  INFO 43516 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-28 14:06:19.805  INFO 43516 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-28 14:06:19.823  INFO 43516 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 0 Redis repository interfaces.
2025-07-28 14:06:19.971  INFO 43516 --- [main] o.s.cloud.context.scope.GenericScope     : BeanFactory id=a28cdf3d-2497-3ef5-9b94-3ee1e1ff5151
2025-07-28 14:06:20.156  INFO 43516 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-28 14:06:20.157  INFO 43516 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-28 14:06:20.158  INFO 43516 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-28 14:06:26.016  INFO 43516 --- [main] c.a.c.s.g.s.SentinelSCGAutoConfiguration : [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
2025-07-28 14:06:26.076  INFO 43516 --- [main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [After]
2025-07-28 14:06:26.076  INFO 43516 --- [main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Before]
2025-07-28 14:06:26.076  INFO 43516 --- [main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Between]
2025-07-28 14:06:26.076  INFO 43516 --- [main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Cookie]
2025-07-28 14:06:26.076  INFO 43516 --- [main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Header]
2025-07-28 14:06:26.076  INFO 43516 --- [main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Host]
2025-07-28 14:06:26.076  INFO 43516 --- [main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Method]
2025-07-28 14:06:26.076  INFO 43516 --- [main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Path]
2025-07-28 14:06:26.076  INFO 43516 --- [main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Query]
2025-07-28 14:06:26.076  INFO 43516 --- [main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [ReadBody]
2025-07-28 14:06:26.076  INFO 43516 --- [main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [RemoteAddr]
2025-07-28 14:06:26.077  INFO 43516 --- [main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [XForwardedRemoteAddr]
2025-07-28 14:06:26.077  INFO 43516 --- [main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Weight]
2025-07-28 14:06:26.077  INFO 43516 --- [main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [CloudFoundryRouteService]
2025-07-28 14:06:26.311  INFO 43516 --- [main] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 1 endpoint(s) beneath base path '/actuator'
2025-07-28 14:06:26.365  INFO 43516 --- [main] c.a.c.s.g.s.SentinelSCGAutoConfiguration : [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
2025-07-28 14:06:26.607  WARN 43516 --- [main] iguration$LoadBalancerCaffeineWarnLogger : Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-07-28 14:06:26.756  INFO 43516 --- [main] o.s.b.web.embedded.netty.NettyWebServer  : Netty started on port 8080
2025-07-28 14:06:28.828  INFO 43516 --- [main] c.a.n.p.a.s.c.ClientAuthPluginManager    : [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-28 14:06:28.829  INFO 43516 --- [main] c.a.n.p.a.s.c.ClientAuthPluginManager    : [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-28 14:06:29.006  INFO 43516 --- [main] c.a.c.n.registry.NacosServiceRegistry    : nacos registry, DEFAULT_GROUP park-gateway 192.168.3.140:8080 register finished
2025-07-28 14:06:29.123  INFO 43516 --- [main] a.c.n.d.NacosDiscoveryHeartBeatPublisher : Start nacos heartBeat task scheduler.
2025-07-28 14:06:29.151  INFO 43516 --- [main] com.lgjy.gateway.LgjyGatewayApplication  : Started LgjyGatewayApplication in 16.341 seconds (JVM running for 17.878)
2025-07-28 14:06:29.160  INFO 43516 --- [main] c.a.c.n.refresh.NacosContextRefresher    : [Nacos Config] Listening config: dataId=park-gateway, group=DEFAULT_GROUP
2025-07-28 14:06:29.162  INFO 43516 --- [main] c.a.c.n.refresh.NacosContextRefresher    : [Nacos Config] Listening config: dataId=park-gateway-dev.yml, group=DEFAULT_GROUP
2025-07-28 14:06:29.163  INFO 43516 --- [main] c.a.c.n.refresh.NacosContextRefresher    : [Nacos Config] Listening config: dataId=park-gateway.yml, group=DEFAULT_GROUP
2025-07-28 14:06:29.164  INFO 43516 --- [main] c.a.c.n.refresh.NacosContextRefresher    : [Nacos Config] Listening config: dataId=common-dev.yml, group=DEFAULT_GROUP
2025-07-28 14:15:40.267  WARN 43516 --- [Thread-6] c.a.nacos.common.notify.NotifyCenter     : [NotifyCenter] Start destroying Publisher
2025-07-28 14:15:40.267  WARN 43516 --- [Thread-2] c.a.n.common.http.HttpClientBeanHolder   : [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-28 14:15:40.268  WARN 43516 --- [Thread-6] c.a.nacos.common.notify.NotifyCenter     : [NotifyCenter] Destruction of the end
2025-07-28 14:15:40.269  WARN 43516 --- [Thread-2] c.a.n.common.http.HttpClientBeanHolder   : [HttpClientBeanHolder] Destruction of the end
2025-07-28 14:15:40.286  INFO 43516 --- [SpringApplicationShutdownHook] c.a.c.n.registry.NacosServiceRegistry    : De-registering from Nacos Server now...
2025-07-28 14:15:40.289  INFO 43516 --- [SpringApplicationShutdownHook] c.a.c.n.registry.NacosServiceRegistry    : De-registration finished.
